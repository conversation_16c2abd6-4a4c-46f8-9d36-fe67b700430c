"use client";

import React, { useState } from "react";
import LinkModal, { LinkFormData } from "@/components/Modals/LinkModal";

interface PartnerDetailsModalProps {
  partner: any;
  isOpen: boolean;
  onClose: () => void;
}

export default function PartnerDetailsModal({
  partner,
  isOpen,
  onClose,
}: PartnerDetailsModalProps) {
  const [isCreateLinkModalOpen, setIsCreateLinkModalOpen] = useState(false);
  const [isCreatingLink, setIsCreatingLink] = useState(false);

  if (!isOpen || !partner) return null;

  // Check if current user is admin (you may need to adjust this based on your auth system)
  const isAdmin =
    typeof window !== "undefined" && localStorage.getItem("admin_token");

  const handleCreateLink = async (linkData: LinkFormData) => {
    try {
      setIsCreatingLink(true);

      const token = localStorage.getItem("admin_token");
      if (!token) {
        throw new Error("Admin authentication required");
      }

      // Prepare the request body for admin link creation
      const requestBody = {
        name: linkData.name,
        url: linkData.url,
        partnerId: partner.id,
        partnerDocumentId: partner.documentId,
        partnerUserId: partner.user?.id?.toString(),
        partnerUserDocumentId: partner.user?.documentId,
        // Include the IDs needed for the admin API
        userId: partner.user?.id?.toString(),
        userDocumentId: partner.user?.documentId,
        referrerId: partner.id,
        referrerDocumentId: partner.documentId,
      };

      const response = await fetch("/api/referrer-links", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create link");
      }

      // Success - close modal and potentially refresh data
      setIsCreateLinkModalOpen(false);

      // You might want to refresh the partner data here or show a success message
      console.log(
        "Link created successfully for partner:",
        partner.user?.email
      );
    } catch (error: any) {
      console.error("Error creating link:", error);
      // You might want to show an error toast here
    } finally {
      setIsCreatingLink(false);
    }
  };

  // Calculate total clicks from all referrer links
  const totalClicks =
    partner.referrer_links?.reduce(
      (sum: number, link: any) => sum + (link.visitors || 0),
      0
    ) || 0;
  const totalConversions = partner.totalConversions || 0;
  const calculatedConversionRate =
    totalClicks > 0 ? ((totalConversions / totalClicks) * 100).toFixed(1) : "_";

  // Use actual data from partner object with "_" as fallback
  const performanceStats = {
    totalRevenue: partner.totalRevenue || "_",
    totalEarnings: partner.totalEarnings || "_",
    totalClicks: totalClicks > 0 ? totalClicks : "_",
    totalCustomers: partner.totalCustomers || "_",
    customers: partner.referrals?.length || 0,
    conversionRate: calculatedConversionRate,
    totalLeads: partner.totalLeads || "_",
    avgOrderValue: partner.avgOrderValue || "_",
  };

  const affiliateLinks = partner.referrer_links || [
    {
      id: 1,
      url: "_",
      name: "_",
      clicks: "_",
      conversions: "_",
      revenue: "_",
      conversionRate: "_",
    },
  ];

  // Use actual commission data from partner.commissionStats
  const commissionHistory = {
    pending: {
      amount: partner.commissionStats?.pendingEarnings || 0,
      count: partner.commissionStats?.pendingCommissions || 0,
      description: `${
        partner.commissionStats?.pendingCommissions || 0
      } pending commissions`,
    },
    readyToPay: {
      amount: partner.commissionStats?.readyEarnings || 0,
      count: partner.commissionStats?.readyCommissions || 0,
      description: `${
        partner.commissionStats?.readyCommissions || 0
      } ready commissions`,
    },
    paid: {
      amount: partner.commissionStats?.paidEarnings || 0,
      count: partner.commissionStats?.paidCommissions || 0,
      description: `${
        partner.commissionStats?.paidCommissions || 0
      } paid commissions`,
    },
  };

  return (
    <div
      className="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm z-50"
      onClick={onClose}
    >
      <div
        className="bg-white dark:bg-gray-800 w-full h-full overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-4 sm:p-6 lg:p-8">
          {/* Header with Back Button */}
          <div className="flex items-center justify-between mb-6 border-b border-gray-200 dark:border-gray-700 pb-4">
            <button
              onClick={onClose}
              className="flex items-center px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Back to Partners
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Close"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Title Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {partner.user?.first_name} {partner.user?.last_name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {partner.user?.email}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-500">
                  Signed up:{" "}
                  {new Date(partner.createdAt).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <span
                  className={`px-3 py-1 text-sm font-medium rounded-full ${
                    partner.referrer_status === "active"
                      ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                      : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
                  }`}
                >
                  {partner.referrer_status?.charAt(0).toUpperCase() +
                    partner.referrer_status?.slice(1)}
                </span>
              </div>
            </div>
          </div>

          {/* Main Content Layout */}
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
            {/* Left Column - Performance & Details */}
            <div className="xl:col-span-3 space-y-8">
              {/* Performance Statistics */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Performance Statistics</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Total Revenue */}
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                <div className="text-blue-600 dark:text-blue-400 mb-2">
                  <svg
                    className="w-8 h-8 mx-auto"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {performanceStats.totalRevenue === "_"
                    ? "_"
                    : `$${performanceStats.totalRevenue.toLocaleString()}`}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Total Revenue
                  <br />
                  All time generated
                </div>
              </div>

              {/* Total Earnings */}
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                <div className="text-green-600 dark:text-green-400 mb-2">
                  <svg
                    className="w-8 h-8 mx-auto"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {performanceStats.totalEarnings === "_"
                    ? "_"
                    : `$${performanceStats.totalEarnings.toLocaleString()}`}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Total Earnings
                  <br />
                  Commission earned
                </div>
              </div>

              {/* Total Clicks */}
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
                <div className="text-purple-600 dark:text-purple-400 mb-2">
                  <svg
                    className="w-8 h-8 mx-auto"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"
                    />
                  </svg>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {performanceStats.totalClicks === "_"
                    ? "_"
                    : performanceStats.totalClicks.toLocaleString()}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Total Clicks
                  <br />
                  Link visits
                </div>
              </div>

              {/* Customers */}
              <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
                <div className="text-orange-600 dark:text-orange-400 mb-2">
                  <svg
                    className="w-8 h-8 mx-auto"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {performanceStats.totalCustomers}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Customers
                  <br />
                  Successful referrals
                </div>
              </div>
            </div>

            {/* Second Row of Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {performanceStats.conversionRate === "_"
                    ? "_"
                    : `${performanceStats.conversionRate}%`}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Conversion Rate
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {performanceStats.totalLeads}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Total Leads
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {performanceStats.avgOrderValue === "_"
                    ? "_"
                    : `$${performanceStats.avgOrderValue}`}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Avg Order Value
                </div>
              </div>
                </div>
              </div>

              {/* Affiliate Links */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Affiliate Links
              </h2>
              <button
                onClick={() => setIsCreateLinkModalOpen(true)}
                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
              >
                🔗 Generate new link
              </button>
            </div>
            <div className="overflow-hidden border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="overflow-x-auto max-h-80 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Link
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Clicks
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Leads
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Conversions
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Conversion Rate
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {affiliateLinks.map((link: any) => (
                      <tr
                        key={link.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700"
                      >
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-xs">
                              {link.url}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              🔸 {link.name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {link.visitors !== undefined ? link.visitors : "_"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {link.leads !== undefined ? link.leads : "_"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {link.conversions !== undefined
                            ? link.conversions
                            : "_"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {(() => {
                            const visitors = link.visitors ?? "_";
                            const conversions = link.conversions ?? "_";
                            if (
                              visitors === "_" ||
                              conversions === "_" ||
                              visitors === 0
                            )
                              return visitors === 0 ? "0%" : "_";
                            const rate = (
                              (conversions / visitors) *
                              100
                            ).toFixed(1);
                            return `${rate}%`;
                          })()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Commission History */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Commission Summary
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Pending Review */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-400">
                    Pending Review
                  </h3>
                  <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-yellow-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="text-2xl font-bold text-yellow-800 dark:text-yellow-400 mb-1">
                  ${commissionHistory.pending.amount.toFixed(2)}
                </div>
                <div className="text-xs text-yellow-700 dark:text-yellow-500 mb-4">
                  {commissionHistory.pending.description}
                </div>
                <div className="w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-2">
                  <div
                    className="bg-yellow-400 h-2 rounded-full"
                    style={{ width: "60%" }}
                  ></div>
                </div>
              </div>

              {/* Ready to Pay */}
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-green-800 dark:text-green-400">
                    Ready to Pay
                  </h3>
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                    <svg
                      className="w-4 h-4 text-green-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>
                <div className="text-2xl font-bold text-green-800 dark:text-green-400 mb-1">
                  ${commissionHistory.readyToPay.amount.toFixed(2)}
                </div>
                <div className="text-xs text-green-700 dark:text-green-500 mb-4">
                  {commissionHistory.readyToPay.description}
                </div>
                <button className="w-full bg-green-600 hover:bg-green-700 text-white text-sm py-2 px-4 rounded-md transition-colors">
                  Process Payout
                </button>
              </div>

              {/* Total Paid */}
              <div className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-300">
                    Total Paid
                  </h3>
                  <div className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">$</span>
                  </div>
                </div>
                <div className="text-2xl font-bold text-gray-800 dark:text-gray-300 mb-1">
                  ${commissionHistory.paid.amount.toFixed(2)}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400 mb-4">
                  {commissionHistory.paid.description}
                </div>
                <button className="w-full border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm py-2 px-4 rounded-md transition-colors">
                  View History
                </button>
              </div>
              </div>
            </div>
            </div>

            {/* Right Sidebar - Quick Actions & Summary */}
            {/* <div className="xl:col-span-1">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <button
                    onClick={() => setIsCreateLinkModalOpen(true)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors text-sm"
                  >
                    🔗 Generate New Link
                  </button>
                  <button className="w-full border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors text-sm">
                    📊 View Full Report
                  </button>
                  <button className="w-full border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg transition-colors text-sm">
                    ✉️ Send Message
                  </button>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Partner Status
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      <span className={`font-medium ${
                        partner.referrer_status === "active"
                          ? "text-green-600 dark:text-green-400"
                          : "text-gray-600 dark:text-gray-400"
                      }`}>
                        {partner.referrer_status?.charAt(0).toUpperCase() + partner.referrer_status?.slice(1)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Code:</span>
                      <span className="font-mono text-xs bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded">
                        {partner.referral_code}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Links:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {partner.referrer_links?.count || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div> */}
          </div>

          {/* Action Buttons */}
          {/* <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              Return to List
            </button>
            <div className="space-x-2">
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Edit Partner
              </button>
              <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                Generate Report
              </button>
            </div>
          </div> */}

          {/* Create Link Modal */}
          {isCreateLinkModalOpen && (
            <LinkModal
              isOpen={isCreateLinkModalOpen}
              onClose={() => setIsCreateLinkModalOpen(false)}
              onSubmit={handleCreateLink}
              isLoading={isCreatingLink}
              title="Generate New Link for Partner"
              isAdmin={true}
              preselectedPartner={{
                id: partner.id,
                documentId: partner.documentId,
                userId: partner.user?.id?.toString(),
                userDocumentId: partner.user?.documentId,
                name:
                  `${partner.user?.first_name || ""} ${
                    partner.user?.last_name || ""
                  }`.trim() || partner.user?.email,
                email: partner.user?.email,
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
}
