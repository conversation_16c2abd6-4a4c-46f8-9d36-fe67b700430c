"use client";
import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import {
  selectAdminData,
  selectAdminDashboardStats,
  selectAdminDashboardStatsLoading,
} from "@/features/selectors";
import { actions as adminActions } from "@/features/admin/admin.slice";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Enhanced StatCard component
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  subtitle?: string;
  loading?: boolean;
  color: "blue" | "green" | "purple" | "orange" | "red" | "indigo";
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  trend,
  subtitle,
  loading = false,
  color,
}) => {
  const colorClasses = {
    blue: "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400",
    green: "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400",
    purple: "bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400",
    orange: "bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400",
    red: "bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400",
    indigo: "bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400",
  };

  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-xl ${colorClasses[color]}`}>
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                {title}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {loading ? (
                  <span className="animate-pulse">...</span>
                ) : (
                  value
                )}
              </p>
              {subtitle && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          {trend && (
            <div className="text-right">
              <div className={`flex items-center space-x-1 ${
                trend.isPositive ? "text-green-600" : "text-red-600"
              }`}>
                <span className="text-sm font-medium">
                  {trend.isPositive ? "↗" : "↘"} {Math.abs(trend.value)}%
                </span>
              </div>
              {trend.label && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {trend.label}
                </p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default function AdminDashboard() {
  const dispatch = useDispatch();
  const adminData = useSelector(selectAdminData);
  const dashboardStats = useSelector(selectAdminDashboardStats);
  const isLoading = useSelector(selectAdminDashboardStatsLoading);

  // State for time filter
  const [timeFilter, setTimeFilter] = useState<'month' | 'all'>('month');

  useEffect(() => {
    // Fetch dashboard stats when component mounts
    dispatch(adminActions.fetchDashboardStats());
  }, [dispatch]);

  // Navigation handlers for View All buttons
  const handleViewAllPartners = () => {
    window.location.hash = "partners";
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-full">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Good morning, {adminData?.firstname}! 👋
            </p>
          </div>
          
          {/* Time Filter */}
          <div className="flex space-x-2">
            <button
              onClick={() => setTimeFilter('month')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                timeFilter === 'month'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              This Month
            </button>
            <button
              onClick={() => setTimeFilter('all')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                timeFilter === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              All Time
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Revenue - Most Prominent */}
        <StatCard
          title={timeFilter === 'month' ? "Revenue (This Month)" : "Total Revenue"}
          value={`$${timeFilter === 'month' ? (dashboardStats?.currentMonthRevenue || 0) : (dashboardStats?.totalRevenue || 0)}`}
          color="green"
          loading={isLoading}
          subtitle={timeFilter === 'month' ? "Monthly performance" : "All-time earnings"}
          trend={timeFilter === 'month' ? {
            value: dashboardStats?.growthRate || 0,
            isPositive: (dashboardStats?.growthRate || 0) >= 0,
            label: "vs last month"
          } : undefined}
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          }
        />

        <StatCard
          title="Total Partners"
          value={dashboardStats?.totalReferrers || 0}
          color="blue"
          loading={isLoading}
          subtitle="Active affiliate partners"
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          }
        />

        <StatCard
          title="Total Clicks"
          value={dashboardStats?.totalClicks || 0}
          color="purple"
          loading={isLoading}
          subtitle="Clicks from partner links"
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
            </svg>
          }
        />

        <StatCard
          title="Total Leads"
          value={dashboardStats?.totalLeads || 0}
          color="orange"
          loading={isLoading}
          subtitle="Sign-ups from partner links"
          icon={
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
        />
      </div>

      {/* Performance Overview - Monthly Revenue Chart */}
      <div className="mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
              <svg className="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span>Performance Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-6">
              {/* Monthly Revenue Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Monthly Revenue
                  </div>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    $38
                  </div>
                </div>
              </div>

              {/* Chart Container */}
              <div className="relative h-40 mb-6">
                <div className="flex items-end justify-between h-full space-x-1">
                  {/* Mock chart data */}
                  {[
                    { month: 'Jan', value: 25, height: '30%' },
                    { month: 'Feb', value: 45, height: '70%' },
                    { month: 'Mar', value: 35, height: '55%' },
                    { month: 'Apr', value: 20, height: '25%' },
                    { month: 'May', value: 22, height: '28%' },
                    { month: 'Jun', value: 40, height: '60%' },
                    { month: 'Jul', value: 45, height: '70%' },
                    { month: 'Aug', value: 15, height: '15%' },
                    { month: 'Sep', value: 65, height: '100%' }, // Highlighted month
                    { month: 'Oct', value: 30, height: '40%' },
                    { month: 'Nov', value: 55, height: '85%' },
                    { month: 'Dec', value: 25, height: '30%' }
                  ].map((data, index) => (
                    <div key={data.month} className="flex flex-col items-center flex-1">
                      <div 
                        className={`w-full rounded-sm transition-all duration-300 hover:opacity-80 ${
                          data.month === 'Sep' 
                            ? 'bg-blue-600 dark:bg-blue-500' 
                            : 'bg-blue-300 dark:bg-blue-700'
                        }`}
                        style={{ height: data.height }}
                      ></div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 font-medium">
                        {data.month}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Chart Statistics */}
              <div className="flex justify-between items-center">
                {/* Click to Lead Rate */}
                <div className="text-center">
                  <div className="text-xl font-bold text-green-600 dark:text-green-400">
                    5.1%
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Click to Lead Rate
                  </div>
                </div>
                
                {/* Lead to Customer Rate */}
                <div className="text-center">
                  <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                    1.8%
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Lead to Customer Rate
                  </div>
                </div>
                
                {/* Avg Revenue per Customer */}
                <div className="text-center">
                  <div className="text-xl font-bold text-orange-600 dark:text-orange-400">
                    $38
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Avg Revenue per Customer
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Sections */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Recent Referred Customers - 70% width */}
        <div className="w-full lg:w-[70%]">
          <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center space-x-2">
                <svg className="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>Recent referred customers</span>
              </CardTitle>
              <button
                onClick={() => window.location.hash = "customers"}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors"
              >
                View all →
              </button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                Loading customers...
              </div>
            ) : dashboardStats?.recentCustomers?.length > 0 || dashboardStats?.recentActivity?.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="border-b border-gray-200 dark:border-gray-700">
                    <tr>
                      <th className="text-left py-3 px-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                        Referred at
                      </th>
                      <th className="text-left py-3 px-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                        Email
                      </th>
                      <th className="text-left py-3 px-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                        Partner
                      </th>
                      <th className="text-right py-3 px-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                        Amount paid
                      </th>
                      <th className="text-right py-3 px-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="space-y-2">
                    {(dashboardStats?.recentCustomers || dashboardStats?.recentActivity || [])
                      .slice(0, 5)
                      .map((customer: any, index: number) => (
                        <tr 
                          key={customer.id || index} 
                          className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                        >
                          <td className="py-4 px-2">
                            <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                              {new Date(customer.createdAt || customer.referred_at || Date.now()).toLocaleDateString('en-US', {
                                month: 'long',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {new Date(customer.createdAt || customer.referred_at || Date.now()).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </td>
                          <td className="py-4 px-2">
                            <div className="text-sm text-gray-900 dark:text-gray-100 truncate max-w-[150px]">
                              {customer.user?.email || customer.referral?.user?.email || customer.email || 'customer.email@...'}
                            </div>
                          </td>
                          <td className="py-4 px-2">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {customer.partner?.name || customer.referrer?.user?.username || customer.referral?.referrer?.user?.username || 'Partner Name'}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[120px]">
                              {customer.partner?.email || customer.referrer?.user?.email || customer.referral?.referrer?.user?.email || 'partner.email@...'}
                            </div>
                          </td>
                          <td className="py-4 px-2 text-right">
                            <div className="text-sm font-bold text-gray-900 dark:text-gray-100">
                              ${customer.amount_paid || customer.total_paid || customer.referral?.total_paid || '0.00'}
                            </div>
                          </td>
                          <td className="py-4 px-2 text-right">
                            <Badge 
                              variant="default"
                              className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 text-xs"
                            >
                              {customer.status || customer.referral_status || 'Active'}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                <svg className="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                No recent customers found
              </div>
            )}
          </CardContent>
        </Card>
        </div>

        {/* Enhanced Top Partners - 30% width */}
        <div className="w-full lg:w-[30%]">
          <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center space-x-2">
                <svg className="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>Top Partners</span>
              </CardTitle>
              <button
                onClick={handleViewAllPartners}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors"
              >
                View All →
              </button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {isLoading ? (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  Loading partners...
                </div>
              ) : dashboardStats?.topReferrers?.length > 0 ? (
                dashboardStats.topReferrers
                  .slice(0, 8)
                  .map((partner: any, index: number) => (
                    <div
                      key={partner.id}
                      className="flex items-center justify-between p-4 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-all duration-200 border border-gray-100 dark:border-gray-700"
                    >
                      <div className="flex items-center space-x-3 min-w-0 flex-1">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0 shadow-sm">
                          {index + 1}
                        </div>
                        <div className="min-w-0 flex-1">
                          <p
                            className="font-semibold text-gray-900 dark:text-gray-100 truncate"
                            title={
                              partner.user?.username || partner.referral_code
                            }
                          >
                            {partner.user?.username || partner.referral_code}
                          </p>
                          <p
                            className="text-sm text-gray-500 dark:text-gray-400 truncate"
                            title={partner.user?.email || "No email"}
                          >
                            {partner.user?.email || "No email"}
                          </p>
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0 ml-3">
                        <p className="font-bold text-green-600 dark:text-green-400 text-lg">
                          ${partner.totalRevenue || 0}
                        </p>
                        <Badge variant="secondary" className="text-xs">
                          {partner.totalConversions} sales
                        </Badge>
                      </div>
                    </div>
                  ))
              ) : (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  <svg className="h-12 w-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  No partners found
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        </div>
      </div>
    </div>
  );
}
