import type { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({ message: "Authentication required" });
    }

    // Build query string from request parameters
    const queryParams = new URLSearchParams();

    // Handle pagination
    if (req.query.page) {
      queryParams.append("pagination[page]", req.query.page as string);
    }
    if (req.query.pageSize) {
      queryParams.append("pagination[pageSize]", req.query.pageSize as string);
    }

    // Handle sorting
    if (req.query.sort) {
      const sortArray = Array.isArray(req.query.sort)
        ? req.query.sort
        : [req.query.sort];
      sortArray.forEach((sortItem) => {
        queryParams.append("sort", sortItem as string);
      });
    }

    // Handle other query parameters
    Object.entries(req.query).forEach(([key, value]) => {
      if (!["page", "pageSize", "sort"].includes(key) && value) {
        if (Array.isArray(value)) {
          value.forEach((v) => queryParams.append(key, v));
        } else {
          queryParams.append(key, value);
        }
      }
    });

    const queryString = queryParams.toString();
    const response = await StrapiClient.getReferralActivities(
      queryString,
      token
    );

    res.status(200).json(response);
  } catch (error: any) {
    console.error("API Error:", error);
    res.status(error.statusCode || 500).json({
      message: error.message || "Internal server error",
    });
  }
}
