/**
 * user-tracking-request service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService(
  'api::user-tracking-request.user-tracking-request',
  ({ strapi }) => ({
    // Get or create user tracking record
    async getUserTracking(userId) {
      // Find existing tracking record
      const existingRecord = await strapi.entityService.findMany(
        'api::user-tracking-request.user-tracking-request',
        {
          filters: {
            users_permissions_user: userId,
          },
          populate: ['subscription_tier'],
        }
      );

      console.log('Existing tracking record:', !!existingRecord);

      // Return existing or create new
      if (existingRecord && existingRecord.length > 0) {
        return existingRecord[0];
      }

      const globalSettings = await strapi.entityService.findMany('api::global.global');

      // Create new tracking record with default values using document API
      const newRecord = await strapi.documents('api::user-tracking-request.user-tracking-request').create({
        data: {
          users_permissions_user: userId,
          request_count: 0, // Monthly request count
          request_limit: globalSettings[0]?.default_free_requests || 25, // Monthly limit
          daily_request_count: 0, // Daily request count for basic tier users
          daily_request_limit: 0, // Will be set when user subscribes to basic tier
          last_request_date: new Date(),
          last_daily_reset_date: null, // Initialize daily reset tracking
          statistics: {}, // Initialize empty statistics object
          subscription_tier: null,
        },
        status: 'published', // Set the status directly to published
      });

      // Refetch the record with populated relations to ensure consistency
      const populatedRecord = await strapi.entityService.findOne(
        'api::user-tracking-request.user-tracking-request',
        newRecord.id,
        {
          populate: ['subscription_tier'],
        }
      );

      return populatedRecord;
    },

    // Track a user request and check if they're within limits
    async trackUserRequest(userId, path = '/') {
      let tracking = await this.getUserTracking(userId);

      // Check if user has basic tier subscription
      const isBasicTier = await this.isBasicTierUser(tracking);

      // For basic tier users, check and perform daily reset if needed
      if (isBasicTier) {
        tracking = await this.checkAndPerformDailyReset(tracking);
      }

      // Check monthly limit (applies to all users)
      if (tracking.request_count >= tracking.request_limit) {
        return {
          allowed: false,
          remaining: 0,
          limit: tracking.request_limit,
          count: tracking.request_count,
          limitType: 'monthly',
          dailyRemaining: isBasicTier ? Math.max(0, tracking.daily_request_limit - tracking.daily_request_count) : null,
        };
      }

      // For basic tier users, also check daily limit
      if (isBasicTier && tracking.daily_request_limit > 0) {
        if (tracking.daily_request_count >= tracking.daily_request_limit) {
          return {
            allowed: false,
            remaining: tracking.request_limit - tracking.request_count, // Monthly remaining
            limit: tracking.request_limit,
            count: tracking.request_count,
            limitType: 'daily',
            dailyRemaining: 0,
            dailyLimit: tracking.daily_request_limit,
            dailyCount: tracking.daily_request_count,
          };
        }
      }

      // Prepare statistics object to track path counts
      const statistics = tracking.statistics || {};
      if (!statistics[path]) {
        statistics[path] = 0;
      }
      statistics[path]++;

      // Prepare update data
      const updateData: any = {
        request_count: tracking.request_count + 1, // Always increment monthly count
        last_request_date: new Date(),
        statistics: statistics,
      };

      // For basic tier users, also increment daily count
      if (isBasicTier) {
        updateData.daily_request_count = tracking.daily_request_count + 1;
      }

      // Update tracking record
      const updated = await strapi.entityService.update(
        'api::user-tracking-request.user-tracking-request',
        tracking.id,
        {
          data: updateData,
        }
      );

      const monthlyRemaining = updated.request_limit - updated.request_count;
      const dailyRemaining = isBasicTier && updated.daily_request_limit > 0
        ? updated.daily_request_limit - updated.daily_request_count
        : null;

      return {
        allowed: true,
        remaining: monthlyRemaining,
        limit: updated.request_limit,
        count: updated.request_count,
        pathStats: statistics[path],
        // Additional info for basic tier users
        dailyRemaining,
        dailyLimit: isBasicTier ? updated.daily_request_limit : null,
        dailyCount: isBasicTier ? updated.daily_request_count : null,
      };
    },

    // Get user stats
    async getUserStats(userId) {
      const tracking = await this.getUserTracking(userId);
      const isBasicTier = await this.isBasicTierUser(tracking);

      const stats: any = {
        // Monthly tracking (applies to all users)
        request_count: tracking.request_count,
        request_limit: tracking.request_limit,
        remaining: tracking.request_limit - tracking.request_count,
        last_request_date: tracking.last_request_date,
        statistics: tracking.statistics || {},

        // Basic tier information
        isBasicTier,
      };

      // Add daily tracking info for basic tier users
      if (isBasicTier) {
        stats.daily = {
          daily_request_count: tracking.daily_request_count || 0,
          daily_request_limit: tracking.daily_request_limit || 0,
          daily_remaining: Math.max(0, (tracking.daily_request_limit || 0) - (tracking.daily_request_count || 0)),
          last_daily_reset_date: tracking.last_daily_reset_date,
        };
      }

      return stats;
    },

    // Reset user counter (admin function)
    async resetUserCounter(userId, resetStatistics = false) {
      const tracking = await this.getUserTracking(userId);

      const updateData: any = {
        request_count: 0,
        last_request_date: new Date(),
      };

      // Optionally reset statistics as well
      if (resetStatistics) {
        updateData.statistics = {};
      }

      return await strapi.entityService.update(
        'api::user-tracking-request.user-tracking-request',
        tracking.id,
        {
          data: updateData,
        }
      );
    },

    // Update user subscription tier
    async updateSubscription(userId, tier, limit = 0, currentPeriodEnd = null, transaction = null) {
      console.log('Updating subscription for user:', userId, 'Tier:', tier.name, 'Limit:', limit);
      const tracking = await this.getUserTracking(userId);

      // Get the subscription tier from the database if it exists
      let newLimit = limit;
      let tierData = null;
      let tierId = null;

      // If tier is a relation object with ID, or a direct ID
      if (typeof tier === 'object' && tier !== null && tier.id) {
        tierId = tier.id;
        tierData = tier;
      } else if (typeof tier === 'number') {
        tierId = tier;

        try {
          tierData = await strapi.entityService.findOne(
            'api::subscription-tier.subscription-tier',
            tierId
          );
        } catch (error) {
          console.error('Error fetching subscription tier by ID:', error);
        }
      } else if (typeof tier === 'string' && tier !== 'free') {
        // If tier is a string (name), find by name
        try {
          const tiers = await strapi.entityService.findMany(
            'api::subscription-tier.subscription-tier',
            {
              filters: {
                name: tier,
                publishedAt: { $ne: null },
              },
            }
          );
          if (tiers && tiers.length > 0) {
            tierData = tiers[0];
            tierId = tierData.id;
          }
        } catch (error) {
          console.error('Error fetching subscription tier by name:', error);
        }
      } else if (tier === 'free') {
        // Find the free tier (price = 0)
        try {
          const tiers = await strapi.entityService.findMany(
            'api::subscription-tier.subscription-tier',
            {
              filters: {
                price: 0,
                publishedAt: { $ne: null },
              },
            }
          );
          if (tiers && tiers.length > 0) {
            tierData = tiers[0];
            tierId = tierData.id;
          }
        } catch (error) {
          console.error('Error fetching free tier:', error);
        }
      }

      // Use tier request_limit if available, otherwise use provided limit or default
      if (tierData && tierData.request_limit && !newLimit) {
        newLimit = tierData.request_limit;
      }

      // Calculate end date
      let endDate = null;

      if (tierData && tierData.price > 0) {
        endDate = new Date();

        if (tierData.duration_days) {
          endDate.setDate(endDate.getDate() + tierData.duration_days);
        } else {
          endDate.setDate(endDate.getDate() + 30); // 30 days as fallback
        }
      }

      console.log('tierId:', tierId);

      const updateData: any = {
        // Keep for backward compatibility
        subscription_tier: tierId,
      };

      if (newLimit) {
        updateData.request_limit = newLimit; // Monthly limit
      }

      if (currentPeriodEnd) {
        updateData.current_period_end = currentPeriodEnd;
      }

      if (transaction) {
        updateData.transaction = transaction;
      }

      // Set daily limits for basic tier users
      if (tierData && tierData.name) {
        const basicTierNames = ['basic-month', 'basic-quarter', 'basic-year'];
        if (basicTierNames.includes(tierData.name)) {
          // Calculate appropriate daily limit for basic tier users
          const monthlyLimit = newLimit || tierData.request_limit || 25;
          const dailyLimit = this.calculateDailyLimit(tierData.name, monthlyLimit);
          updateData.daily_request_limit = dailyLimit;
          updateData.daily_request_count = 0; // Reset daily count when subscription changes
          console.log(`Setting daily limit for basic tier user (${tierData.name}): ${dailyLimit} (monthly: ${monthlyLimit})`);
        } else {
          // Non-basic tier users don't have daily limits
          updateData.daily_request_limit = 0;
          updateData.daily_request_count = 0;
        }
      }

      // Update tracking record
      await strapi.db.query('api::user-tracking-request.user-tracking-request').update({
        where: { id: tracking.id },
        data: updateData,
      });

      // Update user record with subscription tier
      if (userId && tierId !== null) {
        try {
          await strapi.entityService.update('plugin::users-permissions.user', userId, {
            data: {
              subscription_tier: tierId,
            },
          });
          console.log(`User ${userId} subscription tier updated to ${tierId}`);
        } catch (error) {
          console.error('Error updating user subscription tier:', error);
        }
      }

      return;
    },

    // Get start of day in UTC
    getStartOfDayUTC(date = new Date()) {
      return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()));
    },

    // Update daily request limits for all basic tier users
    async updateBasicUsersDailyLimits(newDailyLimit) {
      try {
        console.log(`Starting bulk update of daily limits to ${newDailyLimit} for all basic tier users`);

        // Step 1: Find and assign basic-month tier to eligible users
        const subscriptionTierAssignmentResult = await this.assignBasicMonthTierToEligibleUsers();
        console.log('Subscription tier assignment result:', subscriptionTierAssignmentResult);

        // Step 2: Get all basic tier subscription IDs
        const basicTiers = await strapi.entityService.findMany(
          'api::subscription-tier.subscription-tier',
          {
            filters: {
              name: {
                $containsi: 'basic'
              }
            },
            fields: ['id', 'name']
          }
        );

        if (!basicTiers || basicTiers.length === 0) {
          console.log('No basic tier subscriptions found');
          return {
            updatedCount: 0,
            updatedUsers: [],
            tierAssignmentCount: subscriptionTierAssignmentResult.assignedCount || 0,
            message: 'No basic tier subscriptions found'
          };
        }

        const basicTierIds = basicTiers.map(tier => tier.id);
        console.log(`Found ${basicTiers.length} basic tiers:`, basicTiers.map(t => t.name));

        // Step 3: Find all user tracking requests with basic tier subscriptions
        const basicUserTrackings = await strapi.entityService.findMany(
          'api::user-tracking-request.user-tracking-request',
          {
            filters: {
              subscription_tier: {
                id: {
                  $in: basicTierIds
                }
              }
            },
            populate: ['subscription_tier', 'users_permissions_user'],
            pagination: {
              limit: -1 // Get all records
            }
          }
        );

        if (!basicUserTrackings || basicUserTrackings.length === 0) {
          console.log('No basic tier users found');
          return {
            updatedCount: 0,
            updatedUsers: [],
            tierAssignmentCount: subscriptionTierAssignmentResult.assignedCount || 0,
            message: 'No basic tier users found'
          };
        }

        console.log(`Found ${basicUserTrackings.length} basic tier users to update`);

        // Step 4: Update each user's daily limit
        const updatedUsers = [];
        let updatedCount = 0;

        for (const tracking of basicUserTrackings) {
          try {
            await strapi.entityService.update(
              'api::user-tracking-request.user-tracking-request',
              tracking.id,
              {
                data: {
                  daily_request_limit: newDailyLimit
                }
              }
            );

            updatedUsers.push({
              trackingId: tracking.id,
              userId: (tracking as any).users_permissions_user?.id || null,
              username: (tracking as any).users_permissions_user?.username || 'Unknown',
              tierName: (tracking as any).subscription_tier?.name || 'Unknown',
              previousLimit: tracking.daily_request_limit || 0,
              newLimit: newDailyLimit
            });

            updatedCount++;
            console.log(`Updated user ${(tracking as any).users_permissions_user?.username || tracking.id}: ${tracking.daily_request_limit || 0} -> ${newDailyLimit}`);
          } catch (error) {
            console.error(`Failed to update user tracking ${tracking.id}:`, error);
          }
        }

        console.log(`Successfully updated ${updatedCount} basic tier users`);

        return {
          updatedCount,
          updatedUsers,
          tierAssignmentCount: subscriptionTierAssignmentResult.assignedCount || 0,
          message: `Successfully updated ${updatedCount} basic tier users and assigned basic-month tier to ${subscriptionTierAssignmentResult.assignedCount || 0} eligible users`
        };

      } catch (error) {
        console.error('Error in updateBasicUsersDailyLimits:', error);
        throw error;
      }
    },

    // Assign basic-month subscription tier to eligible users
    async assignBasicMonthTierToEligibleUsers() {
      try {
        console.log('Starting assignment of basic-month tier to eligible users');

        // Step 1: Find users with request_limit = 200 and no subscription_tier
        const eligibleUserTrackings = await strapi.entityService.findMany(
          'api::user-tracking-request.user-tracking-request',
          {
            filters: {
              request_limit: 200,
              subscription_tier: null
            },
            populate: ['users_permissions_user'],
            pagination: {
              limit: -1 // Get all records
            }
          }
        );

        if (!eligibleUserTrackings || eligibleUserTrackings.length === 0) {
          console.log('No eligible users found for basic-month tier assignment');
          return {
            assignedCount: 0,
            assignedUsers: [],
            message: 'No eligible users found'
          };
        }

        console.log(`Found ${eligibleUserTrackings.length} eligible users for basic-month tier assignment`);

        // Step 2: Get the basic-month subscription tier using the subscription-tier service
        const subscriptionTierService = strapi.service('api::subscription-tier.subscription-tier');
        const basicMonthTier = await subscriptionTierService.getBasicMonthlyTier();

        if (!basicMonthTier) {
          console.error('basic-month subscription tier not found');
          return {
            assignedCount: 0,
            assignedUsers: [],
            message: 'basic-month subscription tier not found'
          };
        }

        console.log('Found basic-month tier:', basicMonthTier);

        // Step 3: Update each eligible user
        const assignedUsers = [];
        let assignedCount = 0;
        const errors = [];

        for (const tracking of eligibleUserTrackings) {
          try {
            const userId = (tracking as any).users_permissions_user?.id;
            const username = (tracking as any).users_permissions_user?.username || 'Unknown';

            if (!userId) {
              console.warn(`Skipping tracking record ${tracking.id} - no associated user`);
              continue;
            }

            // Use the numeric ID for database relations (not documentId)
            const tierId = basicMonthTier.id;

            // Use a database transaction to ensure atomicity
            await strapi.db.transaction(async (_trx) => {
              // Update user-tracking-request record
              await strapi.db.query('api::user-tracking-request.user-tracking-request').update({
                where: { id: tracking.id },
                data: {
                  subscription_tier: tierId,
                  request_limit: basicMonthTier.request_limit,
                  daily_request_limit: this.calculateDailyLimit('basic-month', 5),
                  daily_request_count: 0,
                  last_daily_reset_date: null
                }
              });

              // Update user record
              await strapi.db.query('plugin::users-permissions.user').update({
                where: { id: userId },
                data: {
                  subscription_tier: tierId
                }
              });
            });

            assignedUsers.push({
              trackingId: tracking.id,
              userId: userId,
              username: username,
              tierName: basicMonthTier.name,
              requestLimit: tracking.request_limit
            });

            assignedCount++;
            console.log(`Assigned basic-month tier to user ${username} (ID: ${userId})`);

          } catch (error) {
            const errorMsg = `Failed to assign basic-month tier to user tracking ${tracking.id}: ${error.message}`;
            console.error(errorMsg);
            errors.push(errorMsg);
          }
        }

        console.log(`Successfully assigned basic-month tier to ${assignedCount} users`);

        if (errors.length > 0) {
          console.warn(`Encountered ${errors.length} errors during assignment:`, errors);
        }

        return {
          assignedCount,
          assignedUsers,
          errors,
          message: `Successfully assigned basic-month tier to ${assignedCount} users${errors.length > 0 ? ` (${errors.length} errors)` : ''}`
        };

      } catch (error) {
        console.error('Error in assignBasicMonthTierToEligibleUsers:', error);
        throw error;
      }
    },

    // Calculate appropriate daily limit for basic tier users
    calculateDailyLimit(tierName, monthlyLimit) {
      // Define daily limits based on tier type and monthly limit
      const dailyLimits = {
        'basic-month': Math.max(5, Math.floor(monthlyLimit / 30)), // ~1/30th of monthly, minimum 5
        'basic-quarter': Math.max(3, Math.floor(monthlyLimit / 90)), // ~1/90th of monthly, minimum 3
        'basic-year': Math.max(2, Math.floor(monthlyLimit / 365)), // ~1/365th of monthly, minimum 2
      };

      return dailyLimits[tierName] || Math.max(1, Math.floor(monthlyLimit / 30));
    },

    // Check if user has a basic tier subscription
    async isBasicTierUser(tracking) {
      if (!tracking.subscription_tier) {
        return false;
      }

      try {
        let tierData = null;

        // Handle different subscription_tier formats
        if (typeof tracking.subscription_tier === 'object' && tracking.subscription_tier.name) {
          tierData = tracking.subscription_tier;
        } else if (typeof tracking.subscription_tier === 'number') {
          // Fetch tier data by ID
          tierData = await strapi.entityService.findOne(
            'api::subscription-tier.subscription-tier',
            tracking.subscription_tier
          );
        }

        if (!tierData || !tierData.name) {
          return false;
        }

        // Check if tier name starts with 'basic-'
        const basicTierNames = ['basic-month', 'basic-quarter', 'basic-year'];
        return basicTierNames.includes(tierData.name);
      } catch (error) {
        console.error('Error checking if user has basic tier:', error);
        return false;
      }
    },

    // Check if daily reset is needed and perform it
    async checkAndPerformDailyReset(tracking) {
      const todayUTC = this.getStartOfDayUTC();

      let lastResetDate = null;
      if (tracking.last_daily_reset_date) {
        lastResetDate = this.getStartOfDayUTC(new Date(tracking.last_daily_reset_date));
      }

      // Check if reset is needed (no previous reset or last reset was before today)
      const needsReset = !lastResetDate || lastResetDate.getTime() < todayUTC.getTime();

      if (needsReset) {
        console.log(`[Daily Reset] Performing daily reset for user tracking ID ${tracking.id}, previous daily count: ${tracking.daily_request_count}`);

        // Reset ONLY daily request count and update reset date (preserve monthly count)
        const updatedTracking = await strapi.entityService.update(
          'api::user-tracking-request.user-tracking-request',
          tracking.id,
          {
            data: {
              daily_request_count: 0, // Reset daily count only
              last_daily_reset_date: todayUTC.toISOString(),
            },
          }
        );

        console.log(`[Daily Reset] Successfully reset daily count for user tracking ID ${tracking.id}, new daily count: 0`);
        return updatedTracking;
      }

      return tracking;
    },

    // Manual daily reset for testing/admin purposes
    async performManualDailyReset(userId) {
      try {
        const tracking = await this.getUserTracking(userId);
        const isBasicTier = await this.isBasicTierUser(tracking);

        if (!isBasicTier) {
          return {
            success: false,
            message: 'User does not have a basic tier subscription',
            userTier: (tracking as any).subscription_tier
          };
        }

        const todayUTC = this.getStartOfDayUTC();

        await strapi.entityService.update(
          'api::user-tracking-request.user-tracking-request',
          tracking.id,
          {
            data: {
              daily_request_count: 0, // Reset only daily count, preserve monthly
              last_daily_reset_date: todayUTC.toISOString(),
            },
          }
        );

        console.log(`[Manual Daily Reset] Successfully reset user ${userId}, tracking ID ${tracking.id}`);

        return {
          success: true,
          message: 'Daily reset performed successfully',
          previousCount: tracking.request_count,
          newCount: 0,
          resetDate: todayUTC.toISOString()
        };
      } catch (error) {
        console.error(`[Manual Daily Reset] Error resetting user ${userId}:`, error);
        return {
          success: false,
          message: 'Error performing daily reset',
          error: error.message
        };
      }
    },
  })
);
